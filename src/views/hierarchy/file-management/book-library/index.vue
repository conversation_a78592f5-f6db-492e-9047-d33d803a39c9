<template>
    <div class="book-library flex bg-#fff">
        <file-type-tree
            ref="fileTypeTreeRef"
            v-model="params.dictionaryNodeIds"
            type="book"
            @change="handleTreeChange"
        />
        <n-search-table-page
            class="w-[calc(100%-220px)]"
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 1300,
                maxHeight: 'calc(100vh - 420px)',
                rowKey: (row) => row.id,
                'onUpdate:checkedRowKeys': handleCheck
            }"
            :data-api="$apis.nebula.api.v1.book.getBookList"
            :params="params"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :search-table-space="{
                size: 20
            }"
            :search-props="{
                showAdd: false,
                showInput: false,
                inputWidth: '180px'
            }"
            :auto-load="false"
            @reset="onReset"
        >
            <template #search_form_middle>
                <n-input v-model:value="params.number" placeholder="输入书籍编号" style="width: 180px" clearable />
                <n-input v-model:value="params.name" placeholder="输入书籍名称" style="width: 180px" clearable />
                <n-input v-model:value="params.author" placeholder="输入作者/编者" style="width: 180px" clearable />
                <n-input v-model:value="params.publisher" placeholder="输入出版社" style="width: 180px" clearable />

                <n-select
                    v-model:value="params.onBorrow"
                    :options="onBorrowOptions"
                    placeholder="选择借用状态"
                    style="width: 180px"
                    clearable
                />
            </template>
            <template #search_handle_after>
                <n-space justify="center" class="w-200px">
                    <n-permission has="bookLibraryAdd">
                        <n-button type="primary" @click="onAddEdit('add')" :disabled="!treeData?.length">
                            新增
                        </n-button>
                    </n-permission>
                    <n-permission has="bookLibraryImport">
                        <n-button @click="importFile">导入</n-button>
                    </n-permission>
                    <n-permission has="bookLibraryExport">
                        <n-button type="warning" @click="exportFile">导出</n-button>
                    </n-permission>
                </n-space>
            </template>
            <template #table_receiveCount="{ row }">
                <n-popover trigger="hover">
                    <template #trigger>
                        <span>{{ row.receiveCount }}</span>
                    </template>
                    <div>
                        <div v-for="name in row.receiveUserList" :key="name">{{ name }}</div>
                    </div>
                </n-popover>
            </template>
            <template #table_borrowCount="{ row }">
                <n-popover trigger="hover">
                    <template #trigger>
                        <span>{{ row.borrowCount }}</span>
                    </template>
                    <div>
                        <div v-for="name in row.borrowUserList" :key="name">{{ name }}</div>
                    </div>
                </n-popover>
            </template>
            <template #table_onBorrow="{ row }">
                <n-tag v-if="row.onBorrow" size="small" round :bordered="false" type="warning">借用中</n-tag>
                <n-tag v-else size="small" round :bordered="false" type="success">未借用</n-tag>
            </template>
            <template #table_operation="{ row }">
                <n-space justify="center">
                    <template v-for="btn in firstButtons()" :key="btn.label">
                        <n-permission :has="btn.has">
                            <n-button size="tiny" :type="btn.type || 'info'" @click="btn.onClick(row)">{{
                                btn.label
                            }}</n-button>
                        </n-permission>
                    </template>
                    <n-dropdown
                        v-if="moreButtons().length"
                        :options="dropdownOptions(row)"
                        trigger="click"
                        placement="bottom-end"
                    >
                        <n-button size="tiny" type="default">更多</n-button>
                    </n-dropdown>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { BookRow } from '@/api/apis/nebula/api/v1/book';
import useStore from '@/store/modules/main';
import { DataTableColumns } from 'naive-ui';

const params = ref({
    number: '',
    name: '',
    author: '',
    publisher: '',
    onBorrow: null,
    dictionaryNodeIds: [] as string[]
});

const onBorrowOptions = [
    { label: '未借用', value: 'false' },
    { label: '借用中', value: 'true' }
];

const searchTablePageRef = ref();
const checkedRowKeys = ref<string[]>([]);

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'serialNumber',
        width: 60,
        render: (_: any, index: number) => `${index + 1}`
    },
    { title: '书籍编号', key: 'number', align: 'center', fixed: 'left', ellipsis: { tooltip: true } },
    { title: '书籍名称', key: 'name', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '作者/编者', key: 'author', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '出版社', key: 'publisher', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '书籍类别', key: 'bookType', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '总在册数', key: 'registerCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '领用数', key: 'receiveCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '借用数', key: 'borrowCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '借用状态', key: 'onBorrow', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '余存数', key: 'surplusCount', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '操作', key: 'operation', align: 'center', width: '160', fixed: 'right' }
];

const store = useStore();

const visibleButtons = () => {
    const allBtns = [
        {
            label: '编辑',
            has: 'bookLibraryEdit',
            onClick: (row: BookRow) => onAddEdit('edit', row.number || '', row),
            type: 'success' as const
        },
        {
            label: '预览',
            has: 'bookLibraryPreview',
            onClick: (row: BookRow) => onPreview(row),
            type: 'primary' as const
        },
        {
            label: '领用',
            has: 'bookLibraryReceive',
            onClick: (row: BookRow) => onReceiveBorrow(row, 'receive'),
            type: 'info' as const
        },
        {
            label: '借用',
            has: 'bookLibraryBorrow',
            onClick: (row: BookRow) => onReceiveBorrow(row, 'borrow'),
            type: 'info' as const
        },
        {
            label: '领用归还',
            has: 'bookLibraryReceiveReturn',
            onClick: (row: BookRow) => onReceiveBorrowReturn(row, 'receive'),
            type: 'info' as const
        },
        {
            label: '借用归还',
            has: 'bookLibraryBorrowReturn',
            onClick: (row: BookRow) => onReceiveBorrowReturn(row, 'borrow'),
            type: 'info' as const
        },
        { label: '删除', has: 'bookLibraryDelete', onClick: onDelete, type: 'error' as const }
    ];
    return allBtns.filter((btn) => store.permissions.includes(btn.has));
};
const firstButtons = () => {
    const btns = visibleButtons();
    return btns.length > 3 ? btns.slice(0, 2) : btns;
};
const moreButtons = () => {
    const btns = visibleButtons();
    return btns.length > 3 ? btns.slice(2) : [];
};
const dropdownOptions = (row: BookRow) => {
    return moreButtons().map((btn) => ({
        label: btn.label,
        key: btn.label,
        props: {
            onClick: () => btn.onClick(row)
        }
    }));
};

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};
const onReset = () => {
    params.value = {
        number: '',
        name: '',
        author: '',
        publisher: '',
        onBorrow: null,
        dictionaryNodeIds: []
    };
    init();
};

const handleCheck = (checked: any) => {
    checkedRowKeys.value = checked;
};

// 处理树形组件变化
const fileTypeTreeRef = ref();
const treeData = computed(() => {
    return fileTypeTreeRef.value?.treeData;
});
const handleTreeChange = (keys: string[]) => {
    params.value.dictionaryNodeIds = keys;
    init();
};

const onAddEdit = (type: 'add' | 'edit', oId?: string, row?: BookRow) => {
    $alert.dialog({
        title: `${type === 'add' ? '新增' : '编辑'}书籍`,
        width: '60%',
        content: import('./models/book-form.vue'),
        props: {
            oId: oId || '',
            row: row || false,
            categoryOptions: treeData.value?.[0]?.children,
            onSubmit: () => init()
        }
    });
};
const importFile = () => {
    $alert.dialog({
        title: '导入',
        width: '600px',
        content: import('../models/ledger-file-import.vue'),
        props: {
            type: 'book',
            onSave: () => init()
        }
    });
};

const exportFile = () => {
    window.$dialog.info({
        title: '提示',
        content: '确认后将导出该筛选条件下的数据，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.internal.export({
                moduleType: 1,
                params: params.value
            });
            window.$message.info('数据导出成功，请在右上角下拉菜单【数据导出】中查看');
        }
    });
};

const onPreview = (_row: any) => {
    $alert.dialog({
        title: `文件预览: ${_row.bookFileInfo.fileName}`,
        width: '80%',
        content: import('@/components/file-preview.vue'),
        props: {
            id: _row.bookFileInfo.fileId,
            name: _row.bookFileInfo.fileName,
            format: 'pdf'
        }
    });
};

const onReceiveBorrow = (_row: any, type: 'receive' | 'borrow') => {
    $alert.dialog({
        title: `${type === 'receive' ? '领用' : '借用'}书籍`,
        width: '60%',
        content: import('./models/book-use.vue'),
        props: {
            row: _row,
            type: type
        }
    });
};

const onReceiveBorrowReturn = (_row: any, type: 'receive' | 'borrow') => {
    window.$dialog.info({
        title: `${type === 'receive' ? '领用' : '借用'}归还`,
        content: `确认后将${type === 'receive' ? '领用' : '借用'}归还书籍，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            window.$message.success(`书籍${_row.name}归还成功`);
            init();
        }
    });
};

const onDelete = (_row: any) => {
    window.$dialog.error({
        title: '确认提示',
        content: '确认后将删除书籍，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.book.deleteBook(_row.id);
            window.$message.success(`书籍${_row.name}删除成功`);
            init();
        }
    });
};
</script>

<style scoped lang="less"></style>
