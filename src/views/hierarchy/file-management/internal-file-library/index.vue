<template>
    <div class="internal-file-library flex bg-#fff">
        <file-type-tree
            ref="fileTypeTreeRef"
            v-model="params.docCategoryIds"
            type="internal"
            @change="handleTreeChange"
        />
        <n-search-table-page
            class="w-[calc(100%-220px)]"
            ref="searchTablePageRef"
            :data-table-props="{
                columns,
                size: 'small',
                scrollX: 2600,
                maxHeight: 'calc(100vh - 440px)'
            }"
            :params="params"
            :data-api="$apis.nebula.api.v1.internal.list"
            :search-props="{
                showAdd: store.permissions.indexOf('internalFileAdd') > -1,
                showInput: false,
                searchInputPlaceholder: '请输入文件编号、名称 / 原文件编号',
                inputWidth: '280px'
            }"
            :search-table-space="{
                size: 10
            }"
            :pagination-props="{
                showQuickJumper: true,
                showSizePicker: true,
                pageSizes: [10, 15, 20, 30, 50, 100]
            }"
            :auto-load="false"
            @reset="handleReset"
            @add="handleAdd"
        >
            <template #search_form_middle>
                <n-input class="w-198px" v-model:value="params.no" placeholder="请输入文件编号" />
                <n-input class="w-198px" v-model:value="params.name" placeholder="请输入文件名称" />
            </template>
            <template #search_form_after>
                <n-button type="primary" @click="show = !show">{{ show ? '收起' : '更多' }}</n-button>
            </template>
            <template #search_bottom_layout>
                <n-collapse-transition v-show="show">
                    <n-space>
                        <n-input class="w-198px" v-model:value="params.originalNo" placeholder="请输入原文件编号" />
                        <select-tree-organization
                            ref="departmentTreeRef"
                            class="w-198px"
                            v-model:value="params.departmentIds"
                            multiple
                            checkable
                            filterable
                            cascade
                            :show-path="false"
                            maxTagCount="responsive"
                            placeholder="选择编制部门"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.status"
                            :options="statusOptions"
                            clearable
                            placeholder="选择状态"
                        />
                        <n-select
                            class="w-140px"
                            v-model:value="params.hasAttachment"
                            :options="hasAttachmentOptions"
                            clearable
                            placeholder="是否有附件"
                        />
                    </n-space>
                </n-collapse-transition>
            </template>
            <template #search_handle_after>
                <n-permission has="internalFileImport">
                    <n-button @click="handleImport">导入</n-button>
                </n-permission>
                <n-permission has="internalFileExport">
                    <n-button type="warning" @click="handleExport">导出</n-button>
                </n-permission>
            </template>
            <template #table_auditors="{ row }">
                <n-space vertical size="small">
                    <n-tag v-for="item in row.approvalInfo?.auditors" :key="item.id" size="small">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </n-tag>
                    <span v-if="row.approvalInfo?.auditors.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #table_approvers="{ row }">
                <n-space vertical size="small">
                    <n-tag v-for="item in row.approvalInfo?.approvers" :key="item.id" size="small">
                        {{ `${item.userNickname}（${dayjs(item.passedDate).format('YYYY-MM-DD')}）` }}
                    </n-tag>
                    <span v-if="row.approvalInfo?.approvers.length === 0" class="text-gray-400"> - </span>
                </n-space>
            </template>
            <template #table_publishDate="{ row }">
                <n-time :time="row.publishDate" format="yyyy-MM-dd" />
            </template>
            <template #table_effectiveDate="{ row }">
                <n-time :time="row.effectiveDate" format="yyyy-MM-dd" />
            </template>
            <template #table_status="{ row }">
                <n-tag :bordered="false" round size="small" :type="$datas.fileLibrary.statusMap[row.status].type">
                    {{ $datas.fileLibrary.statusMap[row.status].label }}
                </n-tag>
            </template>
            <template #table_todo="{ row }">
                <n-space justify="center" :wrap="false">
                    <n-permission has="internalFileView">
                        <n-button size="tiny" @click="handleView(row)">查阅</n-button>
                    </n-permission>
                    <n-permission has="internalFileBorrow">
                        <n-button size="tiny" type="primary" @click="handleBorrow(row)">借阅</n-button>
                    </n-permission>
                    <n-permission has="internalFileDownload">
                        <n-button size="tiny" type="primary" @click="handleDownload(row)">下载</n-button>
                    </n-permission>
                    <n-dropdown
                        v-if="todoOptions.length > 0"
                        trigger="click"
                        :options="todoOptions(row)"
                        @select="(key) => handleTodoMenu(key, row)"
                    >
                        <n-button size="tiny">更多</n-button>
                    </n-dropdown>
                </n-space>
            </template>
        </n-search-table-page>
    </div>
</template>

<script setup lang="ts">
import { type DataTableColumns } from 'naive-ui';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';

const store = useStore();

const searchTablePageRef = ref();
const params = ref<any>({
    no: '', // 文件编号
    name: '', // 文件名称
    originalNo: '', // 原文件编号
    docCategoryIds: [], // 文件类别
    departmentIds: null, // 编制部门
    status: null, // 状态
    hasAttachment: null // 是否有附件 0 全部 1 有附件 2 无附件
});

const fileTypeTreeRef = ref();
const treeData = computed(() => {
    return fileTypeTreeRef.value?.treeData;
});

const show = ref(false);

const columns: DataTableColumns = [
    {
        title: '序号',
        key: 'key',
        align: 'center',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件编号', key: 'no', align: 'center', fixed: 'left', ellipsis: { tooltip: true }, resizable: true },
    { title: '版本/版次', key: 'versionNo', align: 'center', width: 80, ellipsis: { tooltip: true }, resizable: true },
    { title: '原文件编号', key: 'originalNo', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '原版本/版次',
        key: 'originalVersionNo',
        align: 'center',
        width: 100,
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '文件名称', key: 'name', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '文件类别', key: 'docCategoryName', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    { title: '编制部门', key: 'departmentName', align: 'center', ellipsis: { tooltip: true }, resizable: true },
    {
        title: '编制人',
        key: 'authorNickname',
        align: 'center',
        width: 120,
        ellipsis: { tooltip: true },
        resizable: true
    },
    { title: '审核人', key: 'auditors', align: 'center', width: 170, ellipsis: { tooltip: true }, resizable: true },
    { title: '批准人', key: 'approvers', align: 'center', width: 170, ellipsis: { tooltip: true }, resizable: true },
    { title: '发布日期', key: 'publishDate', align: 'center', width: 120, resizable: true },
    { title: '实施日期', key: 'effectiveDate', align: 'center', width: 120, resizable: true },
    { title: '状态', key: 'status', align: 'center', width: 100, ellipsis: { tooltip: true }, resizable: true },
    { title: '操作', key: 'todo', align: 'center', fixed: 'right', width: 220 }
];

const departmentTreeRef = ref();
const deptOptions = computed(() => {
    return departmentTreeRef.value?.treeOptions;
});

const init = () => {
    nextTick(() => {
        searchTablePageRef.value?.initData();
    });
};

const handleReset = () => {
    params.value = {
        no: '',
        name: '',
        originalNo: '',
        departmentIds: null,
        status: null,
        hasAttachment: null,
        docCategoryIds: params.value.docCategoryIds || []
    };
    init();
};
const handleAdd = () => {
    $alert.dialog({
        title: '新增',
        content: import('./models/internal-file-form.vue'),
        width: '800px',
        props: {
            type: 'add',
            deptOptions: deptOptions.value,
            categoryOptions: treeData.value?.[0]?.children,
            onSave: () => handleReset()
        }
    });
};
const handleImport = () => {
    $alert.dialog({
        title: '导入',
        content: import('../models/ledger-file-import.vue'),
        width: '600px',
        props: {
            type: 'internal',
            onSave: () => handleReset()
        }
    });
};
const handleExport = async () => {
    window.$dialog.info({
        title: '提示',
        content: '确认后将导出该筛选条件下的数据，是否确认？',
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            await $apis.nebula.api.v1.internal.export({
                moduleType: 2,
                params: params.value
            });
            window.$message.info('数据导出成功，请在右上角下拉菜单【数据导出】中查看');
        }
    });
};

/**
 * 修订操作
 */
const handleRevision = (row: any) => {
    $alert.dialog({
        title: '修订',
        content: import('./models/internal-file-form.vue'),
        width: '800px',
        props: {
            id: row.id,
            type: 'revision',
            deptOptions: deptOptions.value,
            categoryOptions: treeData.value?.[0]?.children,
            onSave: () => handleReset()
        }
    });
};
/**
 * 详情操作
 */
const handleDetail = (row: any) => {
    $alert.dialog({
        title: '详情',
        content: import('./models/internal-file-form.vue'),
        width: '800px',
        props: {
            id: row.id,
            type: 'detail',
            deptOptions: deptOptions.value,
            categoryOptions: treeData.value?.[0]?.children
        }
    });
};
/**
 * 发放回收操作
 */
const handleDistribute = (row: any) => {
    $alert.dialog({
        title: '发放回收',
        content: import('../models/distribute-file.vue'),
        style: 'width: 90%; min-width: 1050px; overflow-x: auto;',
        props: {
            row: row,
            type: 'interior',
            onSave: () => handleReset()
        }
    });
};
/**
 * 作废操作
 */
const handleInvalid = (row: any) => {
    console.log('作废', row);
};
import { FilePermissionUtils } from '../models/file-permission-utils';

/**
 * 借阅操作
 */
const handleBorrow = (row: any) => {
    console.log('借阅', row);
};

/**
 * 查阅操作
 */
const handleView = (row: any) => {
    const fileId = row.fileInfo?.fileId || row.id;
    if (!fileId) {
        window.$message.error('文件ID不存在，无法查阅');
        return;
    }
    FilePermissionUtils.handleView(row);
};

/**
 * 下载操作
 */
const handleDownload = (row: any) => {
    const fileId = row.fileInfo?.fileId || row.id;
    if (!fileId) {
        window.$message.error('文件ID不存在，无法下载');
        return;
    }
    FilePermissionUtils.handleDownload(row);
};
/**
 * 变更记录操作
 */
const handleChangeLog = (row: any) => {
    console.log('变更记录', row);
    $alert.dialog({
        title: '变更记录',
        content: import('../models/change-log.vue'),
        width: '80%',
        props: {
            id: row.id
        }
    });
};

// 处理树形组件变化
const handleTreeChange = (keys: string[]) => {
    params.value.docCategoryIds = keys;
    init();
};

// 状态
const statusOptions = $datas.fileLibrary.statusMap
    .filter((item: { value: number }) => item.value !== 0)
    .map((item: { label: string; value: number }) => ({ label: item.label, value: item.value }));

// 是否有附件
const hasAttachmentOptions = ref<any[]>([
    { label: '是', value: 1 },
    { label: '否', value: 2 }
]);

const todoOptions = (row: any) => {
    const options: any[] = [];
    const perms = store.permissions;
    if (perms.includes('internalFileRevision')) options.push({ label: '修订', key: 'revision' });
    if (perms.includes('internalFileDetail')) options.push({ label: '详情', key: 'detail' });
    if (perms.includes('internalFileDistribute')) options.push({ label: '发放回收', key: 'distribute' });
    if (perms.includes('internalFileInvalid') && row.status === 3) options.push({ label: '作废', key: 'invalid' });
    if (perms.includes('internalFileChangeLog')) options.push({ label: '变更记录', key: 'changeLog' });
    return options;
};

function handleTodoMenu(key: string, row: any) {
    switch (key) {
        case 'revision':
            handleRevision(row);
            break;
        case 'detail':
            handleDetail(row);
            break;
        case 'distribute':
            handleDistribute(row);
            break;
        case 'invalid':
            handleInvalid(row);
            break;
        case 'changeLog':
            handleChangeLog(row);
            break;
    }
}
</script>
