<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical size="large">
            <n-descriptions :column="2" size="large" label-placement="left">
                <n-descriptions-item label="发放人">{{ props.row.applicant }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">{{
                    dayjs(props.row.applyDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
                <n-descriptions-item label="发放类型">{{
                    props.row.distributeType === 1 ? '内部发放' : props.row.distributeType === 2 ? '外部发放' : ''
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类型">{{
                    props.row.fileType === 1 ? '内部文件' : props.row.fileType === 2 ? '外部文件' : ''
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类别">{{ props.row.fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">{{ props.row.reason }}</n-descriptions-item>
                <n-descriptions-item label="其他原因" v-if="props.row.otherReason">{{
                    props.row.otherReason
                }}</n-descriptions-item>
            </n-descriptions>
            <p style="font-size: 15px">发放清单</p>
            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: paperList,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 1200
                }"
                :search-props="{ show: false }"
                :table-props="{ showPagination: false }"
            >
                <template #table_personList="{ row }">
                    <div style="line-height: 1.5">
                        <template v-for="(item, index) in (row.paperDocumentOnceDownload?.receivedBy || []).filter((user: any) => user.status > 2)" :key="index">
                            <n-tooltip trigger="hover" placement="top">
                                <template #trigger>
                                    <span 
                                        style="color: #1890ff; text-decoration: underline; cursor: pointer;"
                                    >
                                        {{ item.nickname }}
                                    </span>
                                </template>
                                <span>回收日期：{{ formatRecycleDate(item.recycleDate) }}</span>
                            </n-tooltip>
                            <span v-if="index < (row.paperDocumentOnceDownload?.receivedBy || []).filter((user: any) => user.status > 2).length - 1">，</span>
                        </template>
                        <span v-if="row.recipient" style="margin-left: 8px;">{{ row.recipient }}</span>
                    </div>
                </template>
                <template #table_action="{ row }">
                    <n-button @click="showDisposalRecord(row)" size="tiny" type="primary">处置记录</n-button>
                </template>
            </n-search-table-page>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import AlertContent from '@/components/alert-content.vue';
import { NTooltip } from 'naive-ui';
import dayjs from 'dayjs';

const props = defineProps({ row: { type: Object, default: () => ({}) }, list: { type: Array, default: () => [] } });
console.log(props.row, 'props');
const itemList = ref<any[]>([]);



const formatRecycleDate = (date: string | null) => {
    if (!date) return '暂无';
    return dayjs(date).format('YYYY-MM-DD');
};


// 处理数据，与 paper-dispose-list-table.vue 保持一致
const paperList = computed(() => {
    return itemList.value
        .map((item: any) => {
            // 只显示纸质文件的数据
            if (!item.paperDocumentOnceDownload) return null;
            
            return {
                id: item.id,
                fileId: item.fileId,
                fileName: item.fileName,
                number: item.number,
                version: item.version,
                fileForm: item.paperDocumentOnceDownload.fileForm === 2 ? '纸质文件' : '电子文件',
                distributeCount: item.paperDocumentOnceDownload.distributeCount || 0,
                recycleCount: item.paperDocumentOnceDownload.recycleCount || 0,
                disposalCount: item.paperDocumentOnceDownload.disposalCount || 0,
                paperDocumentOnceDownload: item.paperDocumentOnceDownload
            };
        })
        .filter((item): item is any => item !== null);
});

const columns = [
    { title: '序号', key: 'index', width: 60, render: (_: any, idx: number) => idx + 1 },
    { title: '文件名称', key: 'fileName', width: 160 },
    { title: '文件编号', key: 'number', width: 100 },
    { title: '版本/版次', key: 'version', width: 100 },
    { title: '文件形式', key: 'fileForm', width: 100 },
    { title: '发放份数', key: 'distributeCount', width: 100 },
    { title: '回收份数', key: 'recycleCount', width: 100 },
    { title: '处置份数', key: 'disposalCount', width: 100 },
    {
        title: '交还人',
        key: 'personList',
        width: 200
    },
    { title: '操作', key: 'action', width: 100,fixed: 'right' as const, align: 'center' as const }
];



function showDisposalRecord(row: any) {
    $alert.dialog({
        title: '处置记录',
        content: import('./disposal-record.vue'),
        width: '60%',
        props: { id: row.id }
    });
}
onMounted(async () => {
   
    itemList.value = props.list;
});
</script>

<style scoped></style>
