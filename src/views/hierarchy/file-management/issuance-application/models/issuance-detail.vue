<template>
    <alert-content :showDefaultButtons="false">
        <n-space vertical size="large">
            <n-descriptions :column="2" size="large" label-placement="left">
                <n-descriptions-item label="发放人">{{ row.applicant }}</n-descriptions-item>
                <n-descriptions-item label="发放日期">{{
                    dayjs(row.applyDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
                <n-descriptions-item label="发放类型">{{
                    issuanceTypeOptions.find((item) => item.value === row.distributeType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类型">{{
                    fileTypeOptions.find((item) => item.value === row.fileType)?.label
                }}</n-descriptions-item>
                <n-descriptions-item label="文件类别">{{ row.fileCategory }}</n-descriptions-item>
                <n-descriptions-item label="发放原因">
                    <n-ellipsis>{{ row.reason }}</n-ellipsis>
                </n-descriptions-item>
                <n-descriptions-item label="其他原因" v-if="row.otherReason">{{ row.otherReason }}</n-descriptions-item>
                <n-descriptions-item label="期望发放日期" v-if="row.wishDistributeDate">{{
                    dayjs(row.wishDistributeDate).format('YYYY-MM-DD')
                }}</n-descriptions-item>
            </n-descriptions>

            <n-search-table-page
                :data-table-props="{
                    columns,
                    data: tableData,
                    size: 'small',
                    bordered: true,
                    pagination: false,
                    scrollX: 2000
                }"
                :search-props="{
                    show: false
                }"
                :table-props="{
                    showPagination: false
                }"
            >
                <template #table_eFileLook="{ row }">
                    <p>{{ formatDate(row.eFileLook) }}</p>
                </template>
                <template #table_eFileLookAndDownload="{ row }">
                    <p>{{ formatDate(row.eFileLookAndDownload) }}</p>
                </template>
                <template #table_paperDocumentOnceDownload="{ row }">
                    <p>{{ formatDate(row.paperDocumentOnceDownload) }}</p>
                </template>
                <template #table_eFileOnceDownload="{ row }">
                    <p>{{ formatDate(row.eFileOnceDownload) }}</p>
                </template>
                <template #table_action="{ row }">
                    <n-button @click="showRecycleDialog(row)" size="tiny" type="primary">回收记录</n-button>
                </template>
            </n-search-table-page>
            <span style="font-weight: bold" class="my-20px">发放审批记录</span>
            <n-space vertical size="small">
                <n-steps size="small" vertical :current="approvalSteps.length" direction="vertical">
                    <n-step
                        v-for="(step, index) in approvalSteps"
                        :key="index"
                        :title="step.title"
                        :description="step.description"
                        :status="step.status"
                    />
                </n-steps>
            </n-space>
        </n-space>
    </alert-content>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { NSteps, NStep } from 'naive-ui';
import dayjs from 'dayjs';
const props = defineProps({
    row: { type: Object, default: () => ({}) }
});
const tableData = ref<any[]>([]);
const issuanceTypeOptions = [
    { label: '内部发放', value: 1 },
    { label: '外部发放', value: 2 }
];
const fileTypeOptions = [
    { label: '内部文件', value: 1 },
    { label: '外部文件', value: 2 }
];

// 审批步骤数据
const approvalSteps = computed(() => [
    {
        title: '部门主管审批',
        description: '张三 - 2024-01-15 10:30',
        status: 'finish' as const
    },
    {
        title: '技术总监审核',
        description: '李四 - 2024-01-15 14:20',
        status: 'finish' as const
    },
    {
        title: '质量部确认',
        description: '王五 - 2024-01-16 09:15',
        status: 'finish' as const
    },
    {
        title: '总经理批准',
        description: '赵六 - 2024-01-16 16:45（批准人）',
        status: 'finish' as const
    }
]);

const columns = [
    {
        title: '序号',
        key: 'index',
        width: 60,
        render: (_: any, index: number) => {
            return `${index + 1}`;
        }
    },
    { title: '文件名称', key: 'fileName', width: 100, fixed: 'left' as const },
    { title: '文件编号', key: 'number', width: 100 },
    { title: '版本/版次', key: 'version', width: 100 },
    {
        title: '内发：电子文件-查询',
        key: 'eFileLook',
        width: 200
    },
    {
        title: '内发：电子文件-查询/下载',
        key: 'eFileLookAndDownload',
        width: 200
    },
    {
        title: '内发：纸质文件-一次下载',
        key: 'paperDocumentOnceDownload',
        width: 200
    },
    {
        title: '外发：电子文件-一次下载',
        key: 'eFileOnceDownload',
        width: 200
    },
    {
        title: '操作',
        key: 'action',
        width: 80,
        fixed: 'right' as const,
        align: 'center' as const
    }
];
const formatDate = (data: any) => {
    if (data.receivedBy !== null) {
        return data.receivedBy.map((item: any) => item.nickname).join('，');
    }
    return '';
};

onMounted(async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getDistributeInventory(props.row.id);
    tableData.value = res.data.data;
    console.log(tableData.value);
});

const showRecycleDialog = (row: any) => {
    $alert.dialog({
        title: '回收记录',
        content: import('./recycle-record.vue'),
        width: '60%',
        props: {
            id: row.id
        }
    });
};
</script>
