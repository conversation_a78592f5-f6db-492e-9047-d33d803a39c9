<template>
    <div class="distribution-list-vxe-table">
        <vxe-table
            ref="tableRef"
            :data="flatTableData"
            :border="true"
            :span-method="spanMethod"
            auto-resize
            :edit-rules="validRules"
            :valid-config="{ showMessage: false }"
            :tooltip-config="tooltipConfig"
            :edit-config="{ trigger: 'click', mode: 'cell', showStatus: false }"
        >
            <vxe-column field="fileName" title="文件名称" width="160" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileName"
                        :options="getFileNameOptions(row)"
                        filterable
                        clearable
                        @update:value="(val) => onFileNameSelect(row, val)"
                        placeholder="选择文件名称"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="number" title="文件编号" width="140" :edit-render="{}">
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.number"
                        :options="getFileNoOptions(row)"
                        filterable
                        clearable
                        @update:value="(val) => onFileNoSelect(row, val)"
                        placeholder="选择文件编号"
                        style="width: 100%"
                    />
                </template>
            </vxe-column>
            <vxe-column field="version" title="版本/版次" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.version"
                        placeholder="请输入版本/版次"
                        readonly
                        disabled
                        style="width: 100%"
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column field="fileForm" title="文件形式" min-width="140" :edit-render="{}">
                <template #default="{ row }">
                    <span>{{ getFileFormLabel(row) }}</span>
                </template>
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.fileForm"
                        clearable
                        :options="getFileFormOptions(row)"
                        placeholder="请选择文件形式"
                        :disabled="getFileFormOptions(row).length === 0"
                        @update:value="(val) => handleFileFormChange(row, val)"
                        style="width: 100%"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column field="filePermission" title="文件权限" min-width="120" :edit-render="{}">
                <template #default="{ row }">
                    <span>{{ getFilePermissionLabel(row) }}</span>
                </template>
                <template #edit="{ row }">
                    <n-select
                        v-model:value="row.filePermission"
                        :options="getFilePermissionOptions(row)"
                        placeholder="请选择文件权限"
                        :disabled="getFilePermissionOptions(row).length === 0"
                        @update:value="(val) => handleFilePermissionChange(row, val)"
                        style="width: 100%"
                    ></n-select>
                </template>
            </vxe-column>
            <vxe-column field="receiver" title="接收方" width="120" :edit-render="{}">
                <template #edit="{ row }">
                    <n-input
                        v-model:value="row.receiver"
                        placeholder="请输入接收方"
                        :disabled="!shouldShowReceiver(row)"
                        style="width: 100%"
                    ></n-input>
                </template>
            </vxe-column>
            <vxe-column field="receivedBy" title="接收人" width="180" show-overflow="tooltip" :edit-render="{}">
                <template #default="{ row }">
                    <n-button size="tiny" @click="openPersonModal(row)">
                        {{ row.receivedBy.length > 0 ? `已选择${row.receivedBy.length}人` : '选择接收人' }}
                    </n-button>
                </template>
            </vxe-column>
            <vxe-column field="actions" title="操作" width="145">
                <template #default="{ row }">
                    <n-space :size="5" justify="center">
                        <n-button
                            v-if="row.permIndex === itemList[row.fileIndex].permissions.length - 1"
                            type="primary"
                            size="tiny"
                            @click="addPermission(row.fileIndex)"
                        >
                            增加权限
                        </n-button>
                        <n-button
                            v-if="itemList[row.fileIndex].permissions.length > 1"
                            type="warning"
                            size="tiny"
                            @click="removePermission(row.fileIndex, row.permIndex)"
                        >
                            删除权限
                        </n-button>
                        <n-button
                            v-if="row.permIndex === 0"
                            type="error"
                            size="tiny"
                            @click="removeFile(row.fileIndex)"
                        >
                            删除文件
                        </n-button>
                    </n-space>
                </template>
            </vxe-column>
        </vxe-table>

        <n-modal v-model:show="showPersonModal" preset="card" title="选择接收人" :style="{ width: '800px' }">
            <n-transfer-tree
                ref="transferTree"
                v-model:value="selectPerson"
                :options="allPersonOptions"
                :tree-props="{
                    style: 'margin: 0 4px;',
                    keyField: 'key',
                    labelField: 'label',
                    checkable: true,
                    selectable: false,
                    blockLine: true,
                    checkOnClick: true,
                    cascade: true,
                    checkStrategy: 'child'
                }"
                :transfer-props="{
                    sourceFilterable: true
                }"
            />
            <template #footer>
                <n-space justify="center">
                    <n-button @click="showPersonModal = false">取消</n-button>
                    <n-button type="primary" @click="confirmPersonSelection">确定</n-button>
                </n-space>
            </template>
        </n-modal>
    </div>
</template>

<script setup lang="ts">
import { RowVO } from '@/api/sass/api/v1/dict';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import { NButton, NInput } from 'naive-ui';
import useStore from '@/store/modules/main';

interface PersonItem {
    userId: string;
    userName: string;
}
interface PermissionItem {
    fileForm: string | null;
    filePermission: string | null;
    receiver: string;
    receivedBy: PersonItem[];
}
interface IssuanceItem {
    fileId: string;
    fileName: string;
    number: string;
    version: string;
    permissions: PermissionItem[];
}

const props = defineProps<{
    modelValue: IssuanceItem[];
    issuanceType?: number | null;
    fileType?: number | null;
    fileCategory?: string | null;
}>();
const emit = defineEmits(['update:modelValue']);

const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    fileName: [{ required: true, message: '请输入文件名称' }],
    number: [{ required: true, message: '请输入文件编号' }],
    version: [{ required: true, message: '请输入版本/版次' }],
    fileForm: [{ required: true, message: '请选择文件形式' }],
    filePermission: [{ required: true, message: '请选择文件权限' }],
    receivedBy: [
        {
            required: true,
            validator({ cellValue }) {
                if (!Array.isArray(cellValue) || cellValue.length === 0) {
                    return new Error('请选择接收人');
                }
            }
        }
    ]
});
const itemList = ref<IssuanceItem[]>(props.modelValue || []);
watch(
    () => props.modelValue,
    (newValue) => {
        itemList.value = [...newValue];
    },
    { immediate: true, deep: true }
);

watch(
    [() => props.issuanceType, () => props.fileType, () => props.fileCategory],
    ([newIssuanceType, newFileType, newFileCategory], [oldIssuanceType, oldFileType, oldFileCategory]) => {
        if (
            (newIssuanceType !== oldIssuanceType ||
                newFileType !== oldFileType ||
                newFileCategory !== oldFileCategory) &&
            itemList.value.length === 0
        ) {
            itemList.value = [];
            update();
        }
    }
);

function update() {
    emit('update:modelValue', itemList.value);
}

function addFile() {
    // 检查文件类别是否已选择
    if (!props.fileCategory) {
        window.$message.warning('请先选择文件类别');
        return;
    }

    itemList.value.push({
        fileId: '',
        fileName: '',
        number: '',
        version: '',
        permissions: [
            {
                fileForm: null,
                filePermission: null,
                receiver: '',
                receivedBy: []
            }
        ]
    });
    update();
}
function removeFile(fileIndex: number) {
    itemList.value.splice(fileIndex, 1);
    update();
    // 强制重新渲染表格以更新选项
    nextTick(() => {
        if (tableRef.value) {
            tableRef.value.refreshData();
        }
    });
}
function addPermission(fileIndex: number) {
    itemList.value[fileIndex].permissions.push({
        fileForm: null,
        filePermission: null,
        receiver: '',
        receivedBy: []
    });
    update();
}
function removePermission(fileIndex: number, permIndex: number) {
    itemList.value[fileIndex].permissions.splice(permIndex, 1);
    if (itemList.value[fileIndex].permissions.length === 0) {
        removeFile(fileIndex);
    } else {
        update();
    }
}
const flatTableData = computed(() => {
    const arr: any[] = [];
    itemList.value.forEach((file, fileIndex) => {
        file.permissions.forEach((perm, permIndex) => {
            const processedReceivedBy =
                Array.isArray(perm.receivedBy) && perm.receivedBy.length > 0 && typeof perm.receivedBy[0] === 'object'
                    ? perm.receivedBy.map((p: any) => ({
                          ...p,
                          name: p.name || p.userName || p.userNickname || p.id
                      }))
                    : (perm.receivedBy || []).map((p: any) => ({ id: p, name: p }));

            arr.push({
                fileIndex,
                permIndex,
                fileId: file.fileId,
                fileName: file.fileName,
                number: file.number,
                version: file.version,
                fileForm: perm.fileForm || null,
                filePermission: perm.filePermission || null,
                receiver: perm.receiver,
                receivedBy: processedReceivedBy,
                issuanceType: props.issuanceType || ''
            });
        });
    });
    return arr;
});

function spanMethod({ rowIndex, columnIndex }: { rowIndex: number; columnIndex: number }) {
    const row = flatTableData.value[rowIndex];
    const file = itemList.value[row.fileIndex];
    const permIndex = row.permIndex;
    const permCount = file.permissions.length;
    if ([0, 1, 2].includes(columnIndex)) {
        if (permIndex === 0) {
            return { rowspan: permCount, colspan: 1 };
        } else {
            return { rowspan: 0, colspan: 0 };
        }
    }
    return { rowspan: 1, colspan: 1 };
}

const tableRef = ref();
const tableValid = async () => {
    const $table = tableRef.value;
    if ($table) {
        const errMap = await $table.validate(true);
        if (errMap) {
            // 获取第一个错误信息

            const firstKey = Object.keys(errMap)[0];
            if (firstKey && errMap[firstKey] && errMap[firstKey].length > 0) {
                const firstError = errMap[firstKey][0];
                throw [
                    [
                        {
                            message: firstError.rule?.$options?.message || firstError.rule?.$options?.content
                        }
                    ]
                ];
            }
        }
        return null;
    }
    return null;
};

function shouldShowReceiver(row: any) {
    return row.issuanceType === 2 && row.fileForm === 1 && row.filePermission === 3;
}

function handleFileFormChange(row: any, value: any) {
    row.fileForm = value;
    // 当文件形式改变时，清空文件权限
    if (value !== row.filePermission) {
        row.filePermission = null;
    }
    // 立即更新数据
    const { fileIndex, permIndex } = row;
    itemList.value[fileIndex].permissions[permIndex].fileForm = value;
    if (value !== row.filePermission) {
        itemList.value[fileIndex].permissions[permIndex].filePermission = null;
    }
    update();
}

function handleFilePermissionChange(row: any, value: any) {
    row.filePermission = value;
    // 立即更新数据
    const { fileIndex, permIndex } = row;
    itemList.value[fileIndex].permissions[permIndex].filePermission = value;
    update();
}

watch(
    itemList,
    (newList, oldList) => {
        if (oldList && oldList.length > 0) {
            newList.forEach((file, fileIndex) => {
                file.permissions.forEach((perm, permIndex) => {
                    const oldFile = oldList?.[fileIndex];
                    const oldPerm = oldFile?.permissions?.[permIndex];
                    if (oldPerm && oldFile.fileName && file.fileName !== oldFile.fileName) {
                        perm.receivedBy = [];
                    }
                });
            });
        }
    },
    { deep: true }
);

/**
 * 选择人员弹窗
 */
const showPersonModal = ref(false);
const currentEditFileIndex = ref(-1);
const currentEditPermIndex = ref(-1);

const selectPerson = ref([]);
const allPersonOptions = ref<any[]>([]);
const allUserMap = new Map();
const transformToTreeData = (data: any) => {
    const processOrg = (org: any) => {
        // 构建组织节点
        const treeNode: Record<string, any> = {
            key: org.orgId,
            label: org.orgName,
            parentId: org.parentId,
            isUser: false, // 标记为组织节点
            children: []
        };

        // 处理当前组织下的用户
        if (org.userInfo && org.userInfo.length > 0) {
            org.userInfo.forEach((user: any) => {
                // 将用户信息存入Map
                allUserMap.set(user.userId, {
                    userId: user.userId,
                    userName: user.nickname
                });
                treeNode.children!.push({
                    key: user.userId,
                    label: user.nickname,
                    parentId: org.orgId,
                    isUser: true, // 标记为用户节点
                    children: null
                });
            });
        }

        // 处理子组织
        if (org.children && org.children.length > 0) {
            org.children.forEach((child: any) => {
                treeNode.children!.push(processOrg(child));
            });
        }

        // 如果没有子节点，确保 children 为 null 而不是空数组
        if (treeNode.children!.length === 0) {
            treeNode.children = [];
        }

        return treeNode;
    };

    return [processOrg(data)];
};
// 获取人员列表
const getPersonOptions = async (params: { fileId: string; fileForm: number; filePermission: number }) => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getUserList(params);
    allPersonOptions.value = await transformToTreeData(res.data.organizationUserInfo);
};

function openPersonModal(row: any) {
    if (!row.fileName) {
        window.$message.warning('请先选择文件名称');
        return;
    }
    if (!row.fileForm) {
        window.$message.warning('请先选择文件形式');
        return;
    }
    if (!row.filePermission) {
        window.$message.warning('请先选择文件权限');
        return;
    }

    currentEditFileIndex.value = row.fileIndex;
    currentEditPermIndex.value = row.permIndex;
    const current = row.receivedBy || [];

    // 人员数据回显
    selectPerson.value = current.map((p: any) => p.userId);

    const params = {
        fileId: row.fileId,
        fileForm: row.fileForm,
        filePermission: row.filePermission
    };
    getPersonOptions(params);
    showPersonModal.value = true;
}

function confirmPersonSelection() {
    if (currentEditFileIndex.value >= 0 && currentEditPermIndex.value >= 0) {
        // 获取当前已选择的人员
        const selectedPersons: PersonItem[] = selectPerson.value
            .map((userId: string) => allUserMap.get(userId))
            .filter((user): user is { userId: string; userName: string } => user !== undefined);
        itemList.value[currentEditFileIndex.value].permissions[currentEditPermIndex.value].receivedBy = selectedPersons;
        update();
    }
    showPersonModal.value = false;
}

const store = useStore();
const fileList = ref<any[]>([]);
const fileListLoading = ref(false);

function getOrgTypeForExternal() {
    return store.userInfo.organizationType === 0 ? 1 : 2;
}

async function fetchFileList() {
    fileListLoading.value = true;
    try {
        let list = [];
        if (props.fileType === 1) {
            const res = await $apis.nebula.api.v1.internal.list({
                docCategoryIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        } else if (props.fileType === 2) {
            const orgType = getOrgTypeForExternal();
            const res = await $apis.nebula.api.v1.external.getList({
                typeDictionaryNodeIds: props.fileCategory ? [props.fileCategory] : [],
                status: 3,
                orgType,
                page: 1,
                pageSize: 99999
            });
            list = res.data?.data ?? [];
        }
        fileList.value = Array.isArray(list) ? list : [];
    } finally {
        fileListLoading.value = false;
    }
}

watch(
    () => props.fileCategory,
    () => {
        fetchFileList();
    },
    { immediate: true }
);

function getFileNameOptions(currentRow?: any) {
    // 获取已选择的文件名称（排除当前行）
    const selectedFileNames = itemList.value
        .map((item, index) => ({ fileName: item.fileName, index }))
        .filter(
            (item) =>
                item.fileName && item.fileName.trim() !== '' && (!currentRow || item.index !== currentRow.fileIndex)
        )
        .map((item) => item.fileName);

    return fileList.value
        .filter((file) => !selectedFileNames.includes(file.name))
        .map((file) => ({
            label: file.name,
            value: file.name,
            number: file.no || file.number || file.No,
            version: file.versionNo || file.version
        }));
}
function getFileNoOptions(currentRow?: any) {
    // 获取已选择的文件编号（排除当前行）
    const selectedFileNumbers = itemList.value
        .map((item, index) => ({ number: item.number, index }))
        .filter(
            (item) => item.number && item.number.trim() !== '' && (!currentRow || item.index !== currentRow.fileIndex)
        )
        .map((item) => item.number);

    return fileList.value
        .filter((file) => !selectedFileNumbers.includes(file.no || file.number || file.No))
        .map((file) => ({
            label: file.no || file.number || file.No,
            value: file.no || file.number || file.No,
            fileName: file.name,
            version: file.versionNo || file.version
        }));
}
function onFileNameSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => file.name === selectedValue);
    if (selectedFile) {
        // 检查是否重复选择（排除当前行）
        const isDuplicate = itemList.value.some(
            (item, index) => index !== row.fileIndex && item.fileName === selectedFile.name
        );

        if (isDuplicate) {
            window.$message.warning('该文件已被选择，请选择其他文件');
            // 清空当前选择
            row.fileName = '';
            const { fileIndex } = row;
            if (fileIndex !== undefined && itemList.value[fileIndex]) {
                itemList.value[fileIndex].fileName = '';
                itemList.value[fileIndex].number = '';
                itemList.value[fileIndex].version = '';
                itemList.value[fileIndex].fileId = '';
                update();
            }
            return;
        }

        row.number = selectedFile.no || selectedFile.number || selectedFile.No;
        row.version = selectedFile.versionNo || selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].number = selectedFile.no || selectedFile.number || selectedFile.No;
            itemList.value[fileIndex].version = selectedFile.versionNo || selectedFile.version;
            itemList.value[fileIndex].fileId = selectedFile.id;
            update();
        }
    } else {
        row.fileName = '';
        row.number = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = '';
            itemList.value[fileIndex].number = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].fileId = '';
            update();
        }
    }
}
function onFileNoSelect(row: any, selectedValue: string) {
    const selectedFile = fileList.value.find((file) => (file.no || file.number || file.No) === selectedValue);
    if (selectedFile) {
        // 检查是否重复选择（排除当前行）
        const isDuplicate = itemList.value.some(
            (item, index) =>
                index !== row.fileIndex && item.number === (selectedFile.no || selectedFile.number || selectedFile.No)
        );

        if (isDuplicate) {
            window.$message.warning('该文件已被选择，请选择其他文件');
            // 清空当前选择
            row.number = '';
            const { fileIndex } = row;
            if (fileIndex !== undefined && itemList.value[fileIndex]) {
                itemList.value[fileIndex].fileName = '';
                itemList.value[fileIndex].number = '';
                itemList.value[fileIndex].version = '';
                itemList.value[fileIndex].fileId = '';
                update();
            }
            return;
        }

        row.fileName = selectedFile.name;
        row.version = selectedFile.versionNo || selectedFile.version;
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = selectedFile.name;
            itemList.value[fileIndex].number = selectedFile.no || selectedFile.number || selectedFile.No;
            itemList.value[fileIndex].version = selectedFile.versionNo || selectedFile.version;
            itemList.value[fileIndex].fileId = selectedFile.id;
            update();
        }
    } else {
        row.fileName = '';
        row.number = '';
        row.version = '';
        const { fileIndex } = row;
        if (fileIndex !== undefined && itemList.value[fileIndex]) {
            itemList.value[fileIndex].fileName = '';
            itemList.value[fileIndex].number = '';
            itemList.value[fileIndex].version = '';
            itemList.value[fileIndex].fileId = '';
            update();
        }
    }
}

function receivedByFormatter({ cellValue }: { cellValue: any }) {
    if (Array.isArray(cellValue) && cellValue.length > 0) {
        return cellValue.map((p: any) => p.userName || p.name || p.userNickname || p.id).join('，');
    }
    return '选择接收人';
}

function getFileFormOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    if (issuanceType === 1) {
        return [
            { label: '电子文件', value: 1 },
            { label: '纸质文件', value: 2 }
        ];
    } else if (issuanceType === 2) {
        return [{ label: '电子文件', value: 1 }];
    }
    return [];
}

function getFileFormLabel(row: any) {
    const options = getFileFormOptions(row);
    const option = options.find((opt) => opt.value === row.fileForm);
    return option ? option.label : '';
}

function getFilePermissionLabel(row: any) {
    const options = getFilePermissionOptions(row);
    const option = options.find((opt) => opt.value === row.filePermission);
    return option ? option.label : '';
}

function getFilePermissionOptions(row: any) {
    const issuanceType = row.issuanceType || props.issuanceType;
    const fileForm = row.fileForm;
    if (issuanceType === 1 && fileForm === 1) {
        return [
            { label: '查阅', value: 1 },
            { label: '查阅/下载', value: 2 }
        ];
    } else if (issuanceType === 1 && fileForm === 2) {
        return [{ label: '一次下载', value: 3 }];
    } else if (issuanceType === 2 && fileForm === 1) {
        return [{ label: '一次下载', value: 3 }];
    }
    return [];
}
watch(
    itemList,
    (newList) => {
        newList.forEach((file) => {
            file.permissions.forEach((perm) => {
                const formOpts = getFileFormOptions({ ...perm, issuanceType: props.issuanceType });
                if (!formOpts.some((opt) => String(opt.value) === String(perm.fileForm))) {
                    perm.fileForm = null;
                    perm.filePermission = null;
                }
                const permOpts = getFilePermissionOptions({ ...perm, issuanceType: props.issuanceType });
                if (!permOpts.some((opt) => String(opt.value) === String(perm.filePermission))) {
                    perm.filePermission = null;
                }
            });
        });
    },
    { deep: true }
);
const tooltipConfig = {
    contentMethod({ column, row }: { column: any; row: any }) {
        if (column.field === 'receivedBy') {
            if (Array.isArray(row.receivedBy) && row.receivedBy.length > 0) {
                return row.receivedBy.map((p: any) => p.userName || p.name || p.userNickname || p.id).join('，');
            }
            return '未选择接收人';
        }
    }
};

defineExpose({
    tableValid,
    receivedByFormatter,
    tooltipConfig,
    addFile
});
</script>

<style scoped lang="less">
.table-footer {
    margin-top: 12px;
    text-align: left;
}

.transfer-container {
    display: flex;
    gap: 8px;
    height: 400px;
    .transfer-panel {
        width: 360px;
        border: 1px solid #e0e0e6;
        padding: 12px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        .panel-title {
            font-weight: bold;
        }
        .panel-content {
            flex: 1;
            overflow-y: auto;
            &::-webkit-scrollbar {
                width: 2px;
            }
            &::-webkit-scrollbar-thumb {
                background-color: #ccc;
            }
        }
        .checkbox-item {
            margin-bottom: 4px;
        }
    }
    .transfer-action {
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 8px;
    }
}
</style>
