<template>
    <div class="distribute-file pr-10px">
        <n-grid :cols="2" :x-gap="10" :y-gap="10">
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查阅</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 1
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(1, persons)"
                    @recycle="(keys: string[]) => onBack(1, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：电子文件 - 查阅/下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 2
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(2, persons)"
                    @recycle="(keys: string[]) => onBack(2, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">内发：纸质文件 - 一次下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 2,
                        filePermission: 3
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    @grant="(persons) => onTransfer(3, persons)"
                    @recycle="(keys: string[]) => onBack(3, keys)"
                />
            </n-grid-item>
            <n-grid-item class="flex-v items-center">
                <span class="text-16px font-bold">外发：电子文件 - 一次下载</span>
                <grant-recycle
                    :params="{
                        fileId: row.id,
                        fileForm: 1,
                        filePermission: 3
                    }"
                    :tree-data="treeData"
                    :record-data="recordData"
                    :selected-list="checkTreeData"
                    :hide-recycle-button="true"
                    @grant="(persons) => onTransfer(4, persons)"
                />
            </n-grid-item>
        </n-grid>
        <div class="flex-v items-start mt-10px">
            <span class="text-14px font-500 mt-20px">发放回收记录</span>
            <n-data-table
                :columns="operateColumns"
                :data="operateData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 1)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">内发：纸质文件-一次下载变更记录</span>
            <n-data-table
                :columns="changeColumns"
                :data="changeData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #status="{ row }">
                    <n-tag :type="row.status === '已回收' ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.status
                    }}</n-tag>
                </template>
                <template #disposeStatus="{ row }">
                    <n-tag
                        :type="row.disposeStatus === '已处置' ? 'success' : 'error'"
                        size="small"
                        :bordered="false"
                        >{{ row.disposeStatus }}</n-tag
                    >
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">外发：电子文件-一次下载变更记录</span>
            <n-data-table
                :columns="outDownloadColumns"
                :data="outDownloadData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #isDownload="{ row }">
                    <n-tag :type="row.isDownload ? 'success' : 'error'" size="small" :bordered="false">{{
                        row.isDownload ? '是' : '否'
                    }}</n-tag>
                </template>
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 2)">详情</n-button>
                </template>
            </n-data-table>
            <span class="text-14px font-500 mt-20px">借阅记录</span>
            <n-data-table
                :columns="borrowColumns"
                :data="borrowData"
                size="small"
                max-height="200px"
                :pagination="pagination"
            >
                <template #todo="{ row }">
                    <n-button type="primary" size="tiny" @click="handleApproval(row, 3)">详情</n-button>
                </template>
            </n-data-table>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NTag, PaginationProps } from 'naive-ui';
import { TableColumns } from 'naive-ui/es/data-table/src/interface';
import useStore from '@/store/modules/main';
import dayjs from 'dayjs';

const props = withDefaults(
    defineProps<{
        row?: any;
        type?: string;
    }>(),
    {
        row: {},
        type: 'interior'
    }
);
const emit = defineEmits(['update:id']);
const { row } = useVModels(props, emit);
const store = useStore();
const checkTreeData = ref([]);
const recordData = ref([]);
const treeData = ref([]);

const onTransfer = async (type: number, persons: { key: string; label: string }[]) => {
    // 根据 type 确定文件权限类型、文件形式和发放类型
    const typeConfig: Record<number, { filePermission: number; fileForm: number; distributeType: number }> = {
        1: { filePermission: 1, fileForm: 1, distributeType: 1 }, // 内发：电子文件 - 查询
        2: { filePermission: 2, fileForm: 1, distributeType: 1 }, // 内发：电子文件 - 查询/下载
        3: { filePermission: 3, fileForm: 2, distributeType: 1 }, // 内发：纸质文件 - 一次下载
        4: { filePermission: 3, fileForm: 1, distributeType: 2 } // 外发：电子文件 - 一次下载
    };

    const { filePermission, fileForm, distributeType } = typeConfig[type] || {
        filePermission: 1,
        fileForm: 1,
        distributeType: 1
    };

    // 兼容各种 row 结构
    const fileId = props.row.id || props.row.fileId || '';
    const fileName = (props.row.fileInfo && props.row.fileInfo.fileName) || props.row.name || props.row.fileName || '';
    const number = props.row.number || props.row.no || props.row.fileNo || '';
    const version = props.row.version || props.row.versionNo || '';
    const typeDictNodeId = props.row.typeDictionaryNodeId || props.row.docCategoryId || '';

    const distributeList = [
        {
            fileId,
            fileName,
            number,
            version,
            permissions: [
                {
                    fileForm,
                    filePermission,
                    receiver: '',
                    receivedBy: persons.map((p) => ({
                        userId: p.key,
                        userName: p.label
                    }))
                }
            ]
        }
    ];

    const result = {
        applicant: store.userInfo.nickname,
        applyDate: Date.now(),
        distributeType,
        fileType: props.type === 'interior' ? 1 : 2,
        typeDictNodeId,
        category: props.row.docCategoryName || props.row.docType,
        reason: '其他',
        otherReason: props.type === 'interior' ? '内部文件发放' : '外部文件发放',
        wishDistributeDate: null,
        distributeList
    };

    const formData = JSON.stringify({
        businessId: 'FILE_GRANT',
        version: '1.0.0',
        data: result
    });
    await $hooks.useApprovalProcess('FILE_GRANT', formData);
    window.$message.success('发放申请已提交');

    // 刷新数据
    await refreshData();
};

const onBack = async (type: number, keys: string[]) => {
    // 根据 type 确定文件权限类型和文件形式
    const typeConfig: Record<number, { filePermission: number; fileForm: number }> = {
        1: { filePermission: 1, fileForm: 1 }, // 内发：电子文件 - 查询
        2: { filePermission: 2, fileForm: 1 }, // 内发：电子文件 - 查询/下载
        3: { filePermission: 3, fileForm: 2 }, // 内发：纸质文件 - 一次下载
        4: { filePermission: 3, fileForm: 1 } // 外发：电子文件 - 一次下载
    };

    const { filePermission, fileForm } = typeConfig[type] || { filePermission: 1, fileForm: 1 };

    // 从 recordData 中获取对应权限的人员信息
    const filteredRecordData = recordData.value.filter((item: any) => {
        return item.fileForm === fileForm && item.filePermission === filePermission;
    });

    // 根据 keys 过滤出要回收的人员
    const recycleUsers = filteredRecordData.filter((item: any) => keys.includes(item.userId));

    // 按 inventoryId 分组用户
    const groupedByInventory = new Map();
    recycleUsers.forEach((user: any) => {
        const inventoryId = user.inventoryId;
        if (!groupedByInventory.has(inventoryId)) {
            groupedByInventory.set(inventoryId, []);
        }
        groupedByInventory.get(inventoryId).push(user);
    });

    // 构建 recycleList，按 inventoryId 分组
    const recycleList = Array.from(groupedByInventory.entries()).map(([inventoryId, users]) => ({
        inventoryId: inventoryId,
        permissions: [
            {
                fileForm: fileForm,
                filePermission: filePermission,
                receivedBy: users.map((user: any) => user.userId)
            }
        ]
    }));

    const recycleListInfo = [
        {
            fileName: props.row.fileName || props.row.name || '',
            number: props.row.number || props.row.no || '',
            fileForm: props.row.version || props.row.versionNo || '',
            internalFileRead:
                filePermission === 1
                    ? recycleUsers.map((user: any) => ({
                          label: user.userNickName || user.nickname,
                          value: user.userNickName || user.nickname
                      }))
                    : [],
            internalFileReadAndDownload:
                filePermission === 2
                    ? recycleUsers.map((user: any) => ({
                          label: user.userNickName || user.nickname,
                          value: user.userNickName || user.nickname
                      }))
                    : [],
            internalFileOneDownload:
                filePermission === 3
                    ? recycleUsers.map((user: any) => ({
                          label: user.userNickName || user.nickname,
                          value: user.userNickName || user.nickname
                      }))
                    : []
        }
    ];

    const recycleData = {
        recycleDate: dayjs().valueOf(),
        recycleReason: '其他',
        otherReason: '回收文件',
        recycleList: recycleList,
        // 回收人信息
        recycler: {
            nickname: store.userInfo.nickname,
            recycleDate: dayjs().format('YYYY-MM-DD')
        },
        // 回收原因
        recycleReasonInfo: {
            reason: '其他',
            otherReason: '回收文件'
        },
        // 发放信息
        distributeData: {
            distributeTypeText: type <= 3 ? '内部发放' : '外部发放',
            fileTypeText: props.type === 'interior' ? '内部文件' : '外部文件',
            fileCategory: props.row.docCategoryName || props.row.docType || '',
            wishDistributeDateText: dayjs(new Date().getTime()).format('YYYY-MM-DD')
        },
        recycleListInfo
    };

    const formData = JSON.stringify({
        businessId: 'FILE_RECLAIM',
        version: '1.0.0',
        data: recycleData
    });

    await $hooks.useApprovalProcess('FILE_RECLAIM', formData);
    window.$message.success('回收流程已发起');

    // 刷新数据
    await refreshData();
};

// 统一的刷新方法
const refreshData = async () => {
    await getUserTreeData();
    await getRecord();
};

/**
 * 表格配置
 */
const pagination = ref<PaginationProps>({
    pageSize: 2,
    prefix: ({ itemCount }) => `共 ${itemCount} 条`
});

/**
 * 发放回收记录
 */
const operateColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'applyTime',
        align: 'center',
        width: 180
    },
    {
        title: '内发：电子文件-查询',
        key: 'internalElectronicQuery',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '内发：电子文件-查询/下载',
        key: 'internalElectronicDownload',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '变更人',
        key: 'applicantName',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const operateData = ref([]);
const getOperateData = async () => {
    const res = await $apis.nebula.api.v1.documentLibrary.document.distributeRecords({
        documentId: props.row.id,
        page: 1,
        pageSize: 2
    });
    operateData.value = res.data.data;
};

/**
 * 内发：纸质文件-一次下载变更记录
 */
const changeColumns = ref<TableColumns>([
    {
        title: '纸质文件接收人',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '纸质文件状态',
        key: 'status',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '文件处置状态',
        key: 'disposeStatus',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        width: 180
    },
    {
        title: '回收人',
        key: 'recycler',
        align: 'center',
        width: 180
    },
    {
        title: '回收申请时间',
        key: 'recycleTime',
        align: 'center',
        width: 180
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const changeData = ref([]);
const getChangeData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            receiver: '@cname',
            'status|1': ['已回收'],
            'disposeStatus|1': ['已处置', '未处置', '处置中'],
            issuer: '@cname',
            changer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            recycler: '@cname',
            recycleTime: '@date("yyyy-MM-dd HH:mm:ss")'
        }
    });
    changeData.value = res.data.data;
};

/**
 * 外发：电子文件-一次下载变更记录
 */
const outDownloadColumns = ref<TableColumns>([
    {
        title: '电子文件接收方',
        key: 'receiver',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放人',
        key: 'issuer',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '发放申请时间',
        key: 'issueTime',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '是否下载',
        key: 'isDownload',
        align: 'center',
        width: 80
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const outDownloadData = ref([]);
const getOutDownloadData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            receiver: '@cname',
            issuer: '@cname',
            issueTime: '@date("yyyy-MM-dd HH:mm:ss")',
            isDownload: '@boolean',
            changer: '@cname'
        }
    });
    outDownloadData.value = res.data.data;
};

/**
 * 借阅记录
 */
const borrowColumns = ref<TableColumns>([
    {
        title: '操作时间',
        key: 'time',
        align: 'center',
        width: 180
    },
    {
        title: '借阅人',
        key: 'borrower',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '借阅期限',
        key: 'borrowPeriod',
        align: 'center',
        ellipsis: {
            tooltip: true
        }
    },
    {
        title: '审批流程',
        key: 'todo',
        align: 'center',
        fixed: 'right',
        width: 80
    }
]);

const borrowData = ref([]);

const getBorrowData = async () => {
    const res = await $apis.test.mockData({
        params: { total: 3, noPage: true },
        data: {
            time: '@date("yyyy-MM-dd HH:mm:ss")',
            borrower: '@cname',
            borrowPeriod: '@date("yyyy-MM-dd") 至 @date("yyyy-MM-dd")',
            changer: '@cname'
        }
    });
    borrowData.value = res.data.data;
};

const handleApproval = (row: any, type: number) => {
    $alert.dialog({
        title: '审批详情',
        content: import('./distribute-file-approval-process.vue'),
        width: '50%',
        props: {
            id: row.id,
            type
        }
    });
};
const getRecord = async () => {
    const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
        documentId: props.row.id
    });
    recordData.value = res.data.list;
};
const getUserTreeData = async () => {
    const res = await $apis.nebula.api.v1.issuanceApplication.getUserList({
        fileId: props.row.id
    });

    if (res.data?.organizationUserInfo) {
        treeData.value = res.data.organizationUserInfo;
    } else {
        treeData.value = [];
    }
};
onMounted(async () => {
    await Promise.all([
        getUserTreeData(),
        getRecord(),
        getOperateData(),
        getChangeData(),
        getOutDownloadData(),
        getBorrowData()
    ]);
});
</script>

<style scoped lang="less"></style>
