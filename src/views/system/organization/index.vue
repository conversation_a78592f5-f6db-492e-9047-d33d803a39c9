<template>
    <div class="organization">
        <n-layout has-sider>
            <n-layout-sider
                bordered
                collapse-mode="width"
                :collapsed-width="0"
                :width="siderWidth"
                :min-width="300"
                :max-width="600"
                show-trigger
                @collapse="collapsed = true"
                @expand="collapsed = false"
                :collapsed="collapsed"
                :resizable="true"
                @update:width="handleSiderWidthChange"
            >
                <n-card size="small">
                    <n-space vertical>
                        <n-input-group>
                            <n-input v-model:value="search" placeholder="请输入名称" clearable />
                            <n-button type="primary" @click="organizationTreeRef.filter(search)"> 搜索 </n-button>
                        </n-input-group>
                        <organization-tree ref="organizationTreeRef" @selected="selected" @edit="editBtn" />
                    </n-space>
                </n-card>
            </n-layout-sider>
            <n-layout>
                <n-card size="small" :title="currentInfo.name ?? '暂无数据'" segmented>
                    <n-descriptions v-if="currentId" label-placement="left" :column="2">
                        <n-descriptions-item :label="`${currentType}编码`">{{ currentInfo.code }}</n-descriptions-item>
                        <n-descriptions-item :label="`${currentType}名称`">{{ currentInfo.name }}</n-descriptions-item>
                        <n-descriptions-item :label="currentType + '领导'">{{
                            adminLeaderInfo.leaderName || '未设置'
                        }}</n-descriptions-item>
                        <n-descriptions-item label="是否启用">
                            <n-tag size="small" :type="currentInfo.status ? 'success' : 'error'">
                                {{ currentInfo.status ? '有效' : '禁用' }}
                            </n-tag>
                        </n-descriptions-item>
                        <n-descriptions-item
                            v-if="currentInfo.nodeType === 0 || currentInfo.nodeType === 1"
                            :label="currentType + '管理员'"
                            >{{ adminLeaderInfo.adminName || '未设置' }}</n-descriptions-item
                        >
                    </n-descriptions>
                    <template #footer v-if="currentId">
                        <user ref="userRef" :oId="currentId" @treeList="organizationTreeRef.init()" />
                    </template>
                </n-card>
            </n-layout>
        </n-layout>
    </div>
</template>
<script lang="ts" setup>
import { OrganizationListData } from '@/api/sass/api/v1/organization';
import User from '@/views/system/organization/components/user.vue';

const currentInfo = ref<OrganizationListData>({} as OrganizationListData);
const currentId = ref('');
const currentType = ref('');
const search = ref('');

// 侧边栏相关状态
const collapsed = ref(false);
const siderWidth = ref(300);

// 处理侧边栏宽度变化
const handleSiderWidthChange = (width: number) => {
    siderWidth.value = width;
};

const adminLeaderInfo = ref<{
    leaderName: string;
    adminName: string;
}>({
    leaderName: '',
    adminName: ''
});

const nodeTypeName: Record<number, string> = {
    0: '集团',
    1: '公司',
    2: '部门'
};

const selected = (keys: string[]) => {
    currentId.value = keys[0];
    if (userRef.value) userRef.value.init();
    init();
};

const editBtn = (k: any, row: OrganizationListData) => {
    switch (k) {
        case 'subUnit':
            add(1, nodeTypeName[1], row.id, null);
            break;
        case 'department':
            add(2, nodeTypeName[2], row.id, null);
            break;
        case 'edit':
            add(row.nodeType, nodeTypeName[row.nodeType], row.parentId as string, row);
            break;
        case 'setUpLeaders':
            setLeaders(row.nodeType, nodeTypeName[row.nodeType], row);
            break;
        case 'delete':
            deleteTree(row.id);
            break;
    }
};

const deleteTree = (id: string) => {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.organization.delete([id]);
            window.$message.success('删除成功');
            organizationTreeRef.value.init();
        }
    });
};

const init = async () => {
    const res = await window.api.sass.api.v1.organization.get(currentId.value);

    currentInfo.value = { ...res.data };
    currentType.value = nodeTypeName[currentInfo.value.nodeType];

    const { organizationLeaders, organizationAdmins } = res.data;
    adminLeaderInfo.value = {
        leaderName: organizationLeaders?.map((item: any) => item.leaderName).join(',') ?? '',
        adminName: organizationAdmins?.map((item: any) => item.adminName).join(',') ?? ''
    };
};

const add = (nodeType: number, typeName: string, parentId?: string, row?: OrganizationListData | null) => {
    const isEdit = !!row;
    $alert.dialog({
        title: isEdit ? '编辑' : `新增${typeName}`,
        width: '500px',
        content: import('@/views/system/organization/models/organization-form.vue'),
        props: {
            nodeType,
            typeName,
            parentId,
            row,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const setLeaders = (nodeType: number, typeName: string, row: OrganizationListData | null) => {
    $alert.dialog({
        title: `设置${typeName}领导`,
        width: '500px',
        content: import('@/views/system/organization/models/set-up-leaders-form.vue'),
        props: {
            nodeType,
            typeName,
            id: row?.id,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const organizationTreeRef = ref();
const userRef = ref();
</script>
