<template>
    <div class="organization">
        <div class="split-container">
            <n-split
                direction="horizontal"
                :size="splitSize"
                :min="0.01"
                :max="0.6"
                @update:size="handleSplitSizeChange"
            >
                <template #1>
                    <div class="left-panel" :class="{ collapsed: collapsed }">
                        <div v-if="!collapsed">
                            <n-card size="small">
                                <template #header>
                                    <div class="flex justify-between items-center">
                                        <span>组织架构</span>
                                    </div>
                                </template>
                                <n-space vertical>
                                    <n-input-group>
                                        <n-input v-model:value="search" placeholder="请输入名称" clearable />
                                        <n-button type="primary" @click="organizationTreeRef.filter(search)">
                                            搜索
                                        </n-button>
                                    </n-input-group>
                                    <organization-tree ref="organizationTreeRef" @selected="selected" @edit="editBtn" />
                                </n-space>
                            </n-card>
                        </div>
                        <div v-else class="collapsed-trigger" @click="toggleCollapse">
                            <div class="collapsed-trigger-button">
                                <svg-icon name="svgs-arrow-right" size="12" />
                            </div>
                        </div>
                    </div>
                </template>
                <template #2>
                    <div>
                        <n-card size="small" :title="currentInfo.name ?? '暂无数据'" segmented>
                            <n-descriptions v-if="currentId" label-placement="left" :column="2">
                                <n-descriptions-item :label="`${currentType}编码`">{{
                                    currentInfo.code
                                }}</n-descriptions-item>
                                <n-descriptions-item :label="`${currentType}名称`">{{
                                    currentInfo.name
                                }}</n-descriptions-item>
                                <n-descriptions-item :label="currentType + '领导'">{{
                                    adminLeaderInfo.leaderName || '未设置'
                                }}</n-descriptions-item>
                                <n-descriptions-item label="是否启用">
                                    <n-tag size="small" :type="currentInfo.status ? 'success' : 'error'">
                                        {{ currentInfo.status ? '有效' : '禁用' }}
                                    </n-tag>
                                </n-descriptions-item>
                                <n-descriptions-item
                                    v-if="currentInfo.nodeType === 0 || currentInfo.nodeType === 1"
                                    :label="currentType + '管理员'"
                                    >{{ adminLeaderInfo.adminName || '未设置' }}</n-descriptions-item
                                >
                            </n-descriptions>
                            <template #footer v-if="currentId">
                                <user ref="userRef" :oId="currentId" @treeList="organizationTreeRef.init()" />
                            </template>
                        </n-card>
                    </div>
                </template>
            </n-split>

            <!-- 分割线上的触发器 -->
            <div
                v-if="!collapsed"
                class="split-trigger"
                @click="toggleCollapse"
                :style="{ left: `calc(${splitSize * 100}% - 12px)` }"
            >
                <svg-icon name="svgs-arrow-left" size="12" />
            </div>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { OrganizationListData } from '@/api/sass/api/v1/organization';
import User from '@/views/system/organization/components/user.vue';

const currentInfo = ref<OrganizationListData>({} as OrganizationListData);
const currentId = ref('');
const currentType = ref('');
const search = ref('');

// 分割面板相关状态
const collapsed = ref(false);
const splitSize = ref(0.3); // 左侧面板占比，30%

// 处理分割面板大小变化
const handleSplitSizeChange = (size: number) => {
    if (!collapsed.value) {
        splitSize.value = size;
    }
};

// 切换收起/展开状态
const toggleCollapse = () => {
    collapsed.value = !collapsed.value;
    if (collapsed.value) {
        splitSize.value = 0.02; // 收起时设置更小的比例，约20px宽度
    } else {
        splitSize.value = 0.3; // 展开时恢复默认比例
    }
};

const adminLeaderInfo = ref<{
    leaderName: string;
    adminName: string;
}>({
    leaderName: '',
    adminName: ''
});

const nodeTypeName: Record<number, string> = {
    0: '集团',
    1: '公司',
    2: '部门'
};

const selected = (keys: string[]) => {
    currentId.value = keys[0];
    if (userRef.value) userRef.value.init();
    init();
};

const editBtn = (k: any, row: OrganizationListData) => {
    switch (k) {
        case 'subUnit':
            add(1, nodeTypeName[1], row.id, null);
            break;
        case 'department':
            add(2, nodeTypeName[2], row.id, null);
            break;
        case 'edit':
            add(row.nodeType, nodeTypeName[row.nodeType], row.parentId as string, row);
            break;
        case 'setUpLeaders':
            setLeaders(row.nodeType, nodeTypeName[row.nodeType], row);
            break;
        case 'delete':
            deleteTree(row.id);
            break;
    }
};

const deleteTree = (id: string) => {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.organization.delete([id]);
            window.$message.success('删除成功');
            organizationTreeRef.value.init();
        }
    });
};

const init = async () => {
    const res = await window.api.sass.api.v1.organization.get(currentId.value);

    currentInfo.value = { ...res.data };
    currentType.value = nodeTypeName[currentInfo.value.nodeType];

    const { organizationLeaders, organizationAdmins } = res.data;
    adminLeaderInfo.value = {
        leaderName: organizationLeaders?.map((item: any) => item.leaderName).join(',') ?? '',
        adminName: organizationAdmins?.map((item: any) => item.adminName).join(',') ?? ''
    };
};

const add = (nodeType: number, typeName: string, parentId?: string, row?: OrganizationListData | null) => {
    const isEdit = !!row;
    $alert.dialog({
        title: isEdit ? '编辑' : `新增${typeName}`,
        width: '500px',
        content: import('@/views/system/organization/models/organization-form.vue'),
        props: {
            nodeType,
            typeName,
            parentId,
            row,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const setLeaders = (nodeType: number, typeName: string, row: OrganizationListData | null) => {
    $alert.dialog({
        title: `设置${typeName}领导`,
        width: '500px',
        content: import('@/views/system/organization/models/set-up-leaders-form.vue'),
        props: {
            nodeType,
            typeName,
            id: row?.id,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const organizationTreeRef = ref();
const userRef = ref();
</script>

<style lang="less" scoped>
.left-panel {
    height: 100%;
    transition: all 0.3s ease;

    &.collapsed {
        .collapsed-trigger {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
            border-right: 1px solid #e0e0e0;
            position: relative;

            // 添加一个微妙的内阴影
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.02) 50%, transparent 100%);
                pointer-events: none;
            }

            .collapsed-trigger-button {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                cursor: pointer;
                border-radius: 6px;
                background-color: #fff;
                border: 1px solid #e0e0e0;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                color: #666;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

                &:hover {
                    background-color: #f8f8f8;
                    border-color: #d0d0d0;
                    color: #333;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                    transform: translateX(2px);
                }

                &:active {
                    background-color: #f0f0f0;
                    transform: translateX(1px) scale(0.95);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
            }
        }
    }
}

// 模仿 n-layout-sider 的触发器样式
.sider-trigger {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    cursor: pointer;
    border-radius: 2px;
    background-color: transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #666;

    &:hover {
        background-color: rgba(0, 0, 0, 0.09);
        color: #333;
    }

    &:active {
        background-color: rgba(0, 0, 0, 0.13);
    }
}

// 暗色主题适配
:deep(.n-card.n-card--bordered) {
    .sider-trigger {
        &:hover {
            background-color: rgba(255, 255, 255, 0.09);
        }

        &:active {
            background-color: rgba(255, 255, 255, 0.13);
        }
    }
}

// 分割容器
.split-container {
    position: relative;
    height: 100%;
}

// 分割线上的触发器
.split-trigger {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border: 1px solid #efeff5;
    border-radius: 2px;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: #666;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
        background-color: #f8f8f8;
        border-color: #d9d9d9;
        color: #333;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:active {
        background-color: #f0f0f0;
        transform: translateY(-50%) scale(0.95);
    }
}
</style>
