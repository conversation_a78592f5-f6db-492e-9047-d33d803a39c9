<template>
    <div class="organization">
        <div style="height: calc(100vh - 120px)">
            <n-split
                direction="horizontal"
                :size="splitSize"
                :min="0.15"
                :max="0.6"
                @update:size="handleSplitSizeChange"
            >
                <template #1>
                    <div class="left-panel" :class="{ collapsed: collapsed }">
                        <div v-if="!collapsed" style="padding: 15px">
                            <n-card size="small">
                                <template #header>
                                    <div class="flex justify-between items-center">
                                        <span>组织架构</span>
                                        <n-button text @click="toggleCollapse">
                                            <template #icon>
                                                <svg-icon name="svgs-arrow-left" size="16" />
                                            </template>
                                        </n-button>
                                    </div>
                                </template>
                                <n-space vertical>
                                    <n-input-group>
                                        <n-input v-model:value="search" placeholder="请输入名称" clearable />
                                        <n-button type="primary" @click="organizationTreeRef.filter(search)">
                                            搜索
                                        </n-button>
                                    </n-input-group>
                                    <organization-tree ref="organizationTreeRef" @selected="selected" @edit="editBtn" />
                                </n-space>
                            </n-card>
                        </div>
                        <div v-else class="collapsed-trigger" @click="toggleCollapse">
                            <svg-icon name="svgs-arrow-right" size="16" />
                        </div>
                    </div>
                </template>
                <template #2>
                    <div style="padding: 15px">
                        <n-card size="small" :title="currentInfo.name ?? '暂无数据'" segmented>
                            <n-descriptions v-if="currentId" label-placement="left" :column="2">
                                <n-descriptions-item :label="`${currentType}编码`">{{
                                    currentInfo.code
                                }}</n-descriptions-item>
                                <n-descriptions-item :label="`${currentType}名称`">{{
                                    currentInfo.name
                                }}</n-descriptions-item>
                                <n-descriptions-item :label="currentType + '领导'">{{
                                    adminLeaderInfo.leaderName || '未设置'
                                }}</n-descriptions-item>
                                <n-descriptions-item label="是否启用">
                                    <n-tag size="small" :type="currentInfo.status ? 'success' : 'error'">
                                        {{ currentInfo.status ? '有效' : '禁用' }}
                                    </n-tag>
                                </n-descriptions-item>
                                <n-descriptions-item
                                    v-if="currentInfo.nodeType === 0 || currentInfo.nodeType === 1"
                                    :label="currentType + '管理员'"
                                    >{{ adminLeaderInfo.adminName || '未设置' }}</n-descriptions-item
                                >
                            </n-descriptions>
                            <template #footer v-if="currentId">
                                <user ref="userRef" :oId="currentId" @treeList="organizationTreeRef.init()" />
                            </template>
                        </n-card>
                    </div>
                </template>
            </n-split>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { OrganizationListData } from '@/api/sass/api/v1/organization';
import User from '@/views/system/organization/components/user.vue';

const currentInfo = ref<OrganizationListData>({} as OrganizationListData);
const currentId = ref('');
const currentType = ref('');
const search = ref('');

// 分割面板相关状态
const collapsed = ref(false);
const splitSize = ref(0.3); // 左侧面板占比，30%

// 处理分割面板大小变化
const handleSplitSizeChange = (size: number) => {
    if (!collapsed.value) {
        splitSize.value = size;
    }
};

// 切换收起/展开状态
const toggleCollapse = () => {
    collapsed.value = !collapsed.value;
    if (collapsed.value) {
        splitSize.value = 0.05; // 收起时设置很小的比例
    } else {
        splitSize.value = 0.3; // 展开时恢复默认比例
    }
};

const adminLeaderInfo = ref<{
    leaderName: string;
    adminName: string;
}>({
    leaderName: '',
    adminName: ''
});

const nodeTypeName: Record<number, string> = {
    0: '集团',
    1: '公司',
    2: '部门'
};

const selected = (keys: string[]) => {
    currentId.value = keys[0];
    if (userRef.value) userRef.value.init();
    init();
};

const editBtn = (k: any, row: OrganizationListData) => {
    switch (k) {
        case 'subUnit':
            add(1, nodeTypeName[1], row.id, null);
            break;
        case 'department':
            add(2, nodeTypeName[2], row.id, null);
            break;
        case 'edit':
            add(row.nodeType, nodeTypeName[row.nodeType], row.parentId as string, row);
            break;
        case 'setUpLeaders':
            setLeaders(row.nodeType, nodeTypeName[row.nodeType], row);
            break;
        case 'delete':
            deleteTree(row.id);
            break;
    }
};

const deleteTree = (id: string) => {
    window.$dialog.warning({
        title: '警告',
        content: '确定删除该条数据么？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
            await window.api.sass.api.v1.organization.delete([id]);
            window.$message.success('删除成功');
            organizationTreeRef.value.init();
        }
    });
};

const init = async () => {
    const res = await window.api.sass.api.v1.organization.get(currentId.value);

    currentInfo.value = { ...res.data };
    currentType.value = nodeTypeName[currentInfo.value.nodeType];

    const { organizationLeaders, organizationAdmins } = res.data;
    adminLeaderInfo.value = {
        leaderName: organizationLeaders?.map((item: any) => item.leaderName).join(',') ?? '',
        adminName: organizationAdmins?.map((item: any) => item.adminName).join(',') ?? ''
    };
};

const add = (nodeType: number, typeName: string, parentId?: string, row?: OrganizationListData | null) => {
    const isEdit = !!row;
    $alert.dialog({
        title: isEdit ? '编辑' : `新增${typeName}`,
        width: '500px',
        content: import('@/views/system/organization/models/organization-form.vue'),
        props: {
            nodeType,
            typeName,
            parentId,
            row,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const setLeaders = (nodeType: number, typeName: string, row: OrganizationListData | null) => {
    $alert.dialog({
        title: `设置${typeName}领导`,
        width: '500px',
        content: import('@/views/system/organization/models/set-up-leaders-form.vue'),
        props: {
            nodeType,
            typeName,
            id: row?.id,
            onSave: () => {
                organizationTreeRef.value.init();
                init();
            }
        }
    });
};

const organizationTreeRef = ref();
const userRef = ref();
</script>

<style lang="less" scoped>
.left-panel {
    height: 100%;
    transition: all 0.3s ease;

    &.collapsed {
        .collapsed-trigger {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            width: 100%;
            cursor: pointer;
            background-color: #f5f5f5;
            border-right: 1px solid #e0e0e0;
            transition: background-color 0.2s ease;

            &:hover {
                background-color: #e8e8e8;
            }
        }
    }
}
</style>
