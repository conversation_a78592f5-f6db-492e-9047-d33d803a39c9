export default {
    create(data: DocumentLibraryForm) {
        return request({
            url: '/nebula/api/v1/document-library/loans/add',
            method: 'post',
            data
        });
    },
    update(data: DocumentLibraryForm) {
        return request({
            url: '/nebula/api/v1/document-library/loans/update',
            method: 'post',
            data
        });
    },
    list(data: DocumentLibraryParams) {
        return request({
            url: '/nebula/api/v1/document-library/loans/records',
            method: 'post',
            data
        });
    },
    documents(id: string) {
        return request({
            url: '/nebula/api/v1/document-library/loans/record/documents',
            method: 'get',
            params: { id }
        });
    }
};

export interface DocumentLibraryData {
    /**
     * 申请时间
     */
    approvalApplyTime: number;
    /**
     * 1-待提交，2-待审批，3-已驳回，4-已审批
     */
    approvalStatus?: number;
    approverName?: string;
    borrowDocumentsCount?: number;
    borrowReason?: string;
    borrowTime?: number;
    dueTime?: number;
    /**
     * 借阅记录ID
     */
    id?: string;
    recoverDocumentsCount?: number;
    recoverNames?: string;
    reviewerName?: string;
    userNickname?: string;
    [property: string]: any;
}

export interface DocumentLibraryParams {
    approvalStatus?: string;
    documentCategoryId?: string;
    documentModule?: string;
    documentName?: string;
    documentNo?: string;
    noPage: boolean;
    page: number;
    pageSize: number;
    /**
     * 借阅人名称
     */
    userNickname?: string;
    [property: string]: any;
}

export interface DocumentLibraryForm {
    /**
     * 借阅原因
     */
    borrowReason: string;
    /**
     * 借阅时间
     */
    borrowTime?: number;
    /**
     * 文档列表
     */
    documents: DocumentType[];
    /**
     * 归还时间
     */
    dueTime?: number;
    /**
     * 表单借阅时间
     */
    borrowPeriod?: [number, number] | null;
    [property: string]: any;
}

export interface Document {
    /**
     * 文档id
     */
    documentId: string;
    /**
     * 文档模块，1：书籍 ，2：内部文档 ， 3：外部文档
     */
    documentModuleType: string;
    /**
     * 文档版次号
     */
    documentVersionNo: string;
}

export interface DocumentType extends Document {
    /**
     * 文档有效性
     */
    documentValidity?: number | null;
    /**
     * 文件编号
     */
    documentNo?: string | null;
    /**
     * 文档名称
     */
    documentName?: string | null;
    /**
     * 文档类别
     */
    documentCategory?: string | null;
    /**
     * 虚拟id
     */
    id?: string;
    [property: string]: any;
}

/**
 * 借阅清单列表字段
 */
export interface Documents {
    /**
     * 借阅状态
     */
    borrowStatus?: number;
    documentCategoryName?: string;
    documentId?: string;
    documentModuleName?: string;
    documentName?: string;
    documentNo?: string;
    /**
     * 文档有效性
     */
    documentValidity?: number;
    documentVersionNo?: string;
    [property: string]: any;
}
