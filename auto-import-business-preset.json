{"@/components/business/type-writer.vue": [["default", "TypeWriter"], ["default", "typeWriter"], ["default", "BSTypeWriter"], ["default", "BsTypeWriter"], ["default", "bsTypeWriter"]], "@/components/business/type-writer-demo.vue": [["default", "TypeWriterDemo"], ["default", "typeWriterDemo"], ["default", "BSTypeWriterDemo"], ["default", "BsTypeWriterDemo"], ["default", "bsTypeWriterDemo"]], "@/components/business/todo-notice-list.vue": [["default", "TodoNoticeList"], ["default", "todoNoticeList"], ["default", "BSTodoNoticeList"], ["default", "BsTodoNoticeList"], ["default", "bsTodoNoticeList"]], "@/components/business/todo-notice-card-item.vue": [["default", "TodoNoticeCardItem"], ["default", "todoNoticeCardItem"], ["default", "BSTodoNoticeCardItem"], ["default", "BsTodoNoticeCardItem"], ["default", "bsTodoNoticeCardItem"]], "@/components/business/timeline.vue": [["default", "Timeline"], ["default", "timeline"], ["default", "BSTimeline"], ["default", "BsTimeline"], ["default", "bsTimeline"]], "@/components/business/tab.vue": [["default", "Tab"], ["default", "tab"], ["default", "BSTab"], ["default", "BsTab"], ["default", "bsTab"]], "@/components/business/signature-image.vue": [["default", "SignatureImage"], ["default", "signatureImage"], ["default", "BSSignatureImage"], ["default", "BsSignatureImage"], ["default", "bsSignatureImage"]], "@/components/business/sign-set.vue": [["default", "SignSet"], ["default", "signSet"], ["default", "BSSignSet"], ["default", "BsSignSet"], ["default", "bsSignSet"]], "@/components/business/select-tree-organization.vue": [["default", "SelectTreeOrganization"], ["default", "selectTreeOrganization"], ["default", "BSSelectTreeOrganization"], ["default", "BsSelectTreeOrganization"], ["default", "bsSelectTreeOrganization"]], "@/components/business/select-tree-dictionary.vue": [["default", "SelectTreeDictionary"], ["default", "selectTreeDictionary"], ["default", "BSSelectTreeDictionary"], ["default", "BsSelectTreeDictionary"], ["default", "bsSelectTreeDictionary"]], "@/components/business/recycle-form.vue": [["default", "RecycleForm"], ["default", "recycleForm"], ["default", "BSRecycleForm"], ["default", "BsRecycleForm"], ["default", "bsRecycleForm"]], "@/components/business/radio.vue": [["default", "Radio"], ["default", "radio"], ["default", "BSRadio"], ["default", "BsRadio"], ["default", "bsRadio"]], "@/components/business/process-lack-personnel.vue": [["default", "ProcessLackPersonnel"], ["default", "processLackPersonnel"], ["default", "BSProcessLackPersonnel"], ["default", "BsProcessLackPersonnel"], ["default", "bsProcessLackPersonnel"]], "@/components/business/process-lack-department.vue": [["default", "ProcessLackDepartment"], ["default", "processLackDepartment"], ["default", "BSProcessLackDepartment"], ["default", "BsProcessLackDepartment"], ["default", "bsProcessLackDepartment"]], "@/components/business/people-select.vue": [["default", "PeopleSelect"], ["default", "peopleSelect"], ["default", "BSPeopleSelect"], ["default", "BsPeopleSelect"], ["default", "bsPeopleSelect"]], "@/components/business/paper-disposal-form.vue": [["default", "PaperDisposalForm"], ["default", "paperDisposalForm"], ["default", "BSPaperDisposalForm"], ["default", "BsPaperDisposalForm"], ["default", "bsPaperDisposalForm"]], "@/components/business/notice-model.vue": [["default", "NoticeModel"], ["default", "noticeModel"], ["default", "BSNoticeModel"], ["default", "BsNoticeModel"], ["default", "bsNoticeModel"]], "@/components/business/minio-upload.vue": [["default", "MinioUpload"], ["default", "minioUpload"], ["default", "BSMinioUpload"], ["default", "BsMinioUpload"], ["default", "bsMinioUpload"]], "@/components/business/json-editor.vue": [["default", "JsonEditor"], ["default", "jsonEditor"], ["default", "BSJsonEditor"], ["default", "BsJsonEditor"], ["default", "bsJsonEditor"]], "@/components/business/inclusion-process.vue": [["default", "InclusionProcess"], ["default", "inclusionProcess"], ["default", "BSInclusionProcess"], ["default", "BsInclusionProcess"], ["default", "bsInclusionProcess"]], "@/components/business/image.vue": [["default", "Image"], ["default", "image"], ["default", "BSImage"], ["default", "BsImage"], ["default", "bsImage"]], "@/components/business/grant-recycle.vue": [["default", "GrantRecycle"], ["default", "grantRecycle"], ["default", "BSGrantRecycle"], ["default", "BsGrantRecycle"], ["default", "bsGrantRecycle"]], "@/components/business/form-text.vue": [["default", "FormText"], ["default", "formText"], ["default", "BSFormText"], ["default", "BsFormText"], ["default", "bsFormText"]], "@/components/business/file-type-tree.vue": [["default", "FileTypeTree"], ["default", "fileTypeTree"], ["default", "BSFileTypeTree"], ["default", "BsFileTypeTree"], ["default", "bsFileTypeTree"]], "@/components/business/debug-page.vue": [["default", "DebugPage"], ["default", "debugPage"], ["default", "BSDebugPage"], ["default", "BsDebugPage"], ["default", "bsDebugPage"]], "@/components/business/custom-approval-form.vue": [["default", "CustomApprovalForm"], ["default", "customApprovalForm"], ["default", "BSCustomApprovalForm"], ["default", "BsCustomApprovalForm"], ["default", "bsCustomApprovalForm"]], "@/hooks/useSSE.ts": [["default", "UseSseHooks"], ["default", "useSseHooks"]], "@/hooks/useApprovalProcess.ts": [["default", "UseApprovalProcessHooks"], ["default", "useApprovalProcessHooks"]], "@/hooks/index.ts": [["default", "IndexHooks"], ["default", "indexHooks"]]}